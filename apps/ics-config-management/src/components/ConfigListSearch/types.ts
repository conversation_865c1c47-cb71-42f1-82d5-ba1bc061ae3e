import { MouseEvent } from 'react';
import type { PopupState } from 'material-ui-popup-state/hooks';
import type { ControllerRenderProps } from 'react-hook-form';
import type { DeviceConfig, InstancesRequest } from '../../constants/types';

export type UserOptions = {
  label: string;
  id: string;
  email?: string;
};

export type InstanceFilterFormData = {
  appDescriptorId: string;
  configFileId: string;
  createdBy: UserOptions | null;
  editedBy: UserOptions | null;
  approvalStatus: string;
};

export type InstanceFilterFormProps = {
  view: InstanceView;
  popupState: PopupState;
  defaultValues: InstanceFilterFormData;
  deviceConfigs: DeviceConfig[];
  onFormSubmit: (data: InstancesRequest) => void;
  onClearFilters?: () => void;
};

export type UserAutocompleteProps = {
  field: ControllerRenderProps<
    InstanceFilterFormData,
    'createdBy' | 'editedBy'
  >;
  label: string;
};

export enum InstanceSortOrder {
  asc = 'asc',
  desc = 'desc',
}

export enum InstanceSortOrderBy {
  name = 'name',
  createdDate = 'createdDate',
  lastUpdated = 'lastUpdated',
  sites = 'sites',
  devices = 'devices',
}

export type InstanceSortFormData = {
  order: InstanceSortOrder;
  orderBy: InstanceSortOrderBy;
};

export type InstanceSortFormProps = {
  popupState: PopupState;
  defaultValues: InstanceSortFormData;
  onFormSubmit: (data: InstanceSortFormData) => void;
  resetValues: InstanceSortFormData;
};

export enum InstanceView {
  ALL_INSTANCES = 'ALL_INSTANCES',
  APPLICATIONS = 'APPLICATIONS',
}

export type ViewToggleButtonGroupProps = {
  view: InstanceView;
  handleViewChange: (
    event: MouseEvent<HTMLElement>,
    view: InstanceView
  ) => void;
};
